import '@testing-library/jest-dom';

// Mock Electron APIs for testing
global.window.electronAPI = {
  getAppVersion: jest.fn().mockResolvedValue('1.0.0'),
  getPlatform: jest.fn().mockResolvedValue('win32'),
  performAsyncOperation: jest.fn().mockResolvedValue('Operation completed successfully!'),
  sendMessage: jest.fn().mockResolvedValue('Message sent'),
  onUpdateAvailable: jest.fn(),
  removeAllListeners: jest.fn()
};

global.window.nodeAPI = {
  joinPath: jest.fn((...paths) => paths.join('/')),
  platform: 'win32',
  arch: 'x64'
};

// Mock process.versions for Electron environment
Object.defineProperty(global.process, 'versions', {
  value: {
    electron: '38.0.0',
    node: '20.0.0',
    chrome: '120.0.0',
    v8: '12.0.0'
  },
  writable: true,
  configurable: true
});
