import { Box, Button, CircularProgress } from '@mui/material';
import { useApp } from '../../context/AppContext';

function ActionButtons({ onShowInfo }) {
  const { state, actions } = useApp();
  const { isLoading } = state;

  const handleAsyncOperation = async () => {
    try {
      await actions.performAsyncOperation();
    } catch (error) {
      // Error is already handled in the context
      console.error('Async operation failed:', error);
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      gap: 2,
      justifyContent: 'center',
      flexWrap: 'wrap',
      mb: 2.5
    }}>
      <Button
        variant="contained"
        onClick={handleAsyncOperation}
        disabled={isLoading}
        startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
      >
        {isLoading ? 'Processing...' : 'Test Async Operation'}
      </Button>
      <Button
        variant="contained"
        color="secondary"
        onClick={onShowInfo}
        disabled={isLoading}
      >
        Show App Info
      </Button>
    </Box>
  );
}

export default ActionButtons;
