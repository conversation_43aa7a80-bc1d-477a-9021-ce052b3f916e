import { Grid, Card, CardContent, Typography } from '@mui/material';
import { useApp } from '../../context/AppContext';

function AppInfoGrid() {
  const { state } = useApp();
  const { appInfo } = state;

  const infoItems = [
    { label: 'App Version', value: appInfo.version },
    { label: 'Platform', value: appInfo.platform },
    { label: 'Electron Version', value: appInfo.electronVersion },
    { label: 'Node.js Version', value: appInfo.nodeVersion }
  ];

  return (
    <Grid container spacing={2.5} sx={{ mb: 3.75 }}>
      {infoItems.map((item, index) => (
        <Grid item xs={12} sm={6} key={index}>
          <Card elevation={1}>
            <CardContent sx={{ p: 2 }}>
              <Typography variant="subtitle2" component="h3" sx={{ mb: 0.625 }}>
                {item.label}
              </Typography>
              <Typography variant="h5" component="p">
                {item.value}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
}

export default AppInfoGrid;
