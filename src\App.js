import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AppProvider } from './context/AppContext';
import Header from './components/Header/Header';
import MainContent from './components/MainContent/MainContent';
import Footer from './components/Footer/Footer';
import StatusMessage from './components/StatusMessage/StatusMessage';
import muiTheme from './theme/muiTheme';
import './App.css';

function App() {
  return (
    <ThemeProvider theme={muiTheme}>
      <CssBaseline />
      <AppProvider>
        <div className="app">
          <Header />
          <MainContent />
          <Footer />
          <StatusMessage />
        </div>
      </AppProvider>
    </ThemeProvider>
  );
}

export default App;
