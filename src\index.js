import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import './styles/global.css';

// Check if electronAPI is available (from preload script)
if (typeof window.electronAPI === 'undefined') {
  console.error('electronAPI not available. Make sure preload script is loaded.');
}

const container = document.getElementById('root');
const root = createRoot(container);

root.render(<App />);

// Handle any uncaught errors in the renderer process
window.addEventListener('error', (event) => {
  console.error('Renderer error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});
