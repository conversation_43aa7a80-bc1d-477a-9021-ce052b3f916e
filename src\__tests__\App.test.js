import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../App';

describe('App', () => {
  test('renders the main heading', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('🚀 Electron React App')).toBeInTheDocument();
    });
  });

  test('displays welcome message', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText(/Welcome to Your Electron React App!/)).toBeInTheDocument();
    });
  });

  test('shows app version information', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('App Version')).toBeInTheDocument();
      expect(screen.getByText('1.0.0')).toBeInTheDocument();
    });
  });

  test('async operation button works', async () => {
    const user = userEvent.setup();
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Async Operation')).toBeInTheDocument();
    });

    const asyncButton = screen.getByText('Test Async Operation');
    await user.click(asyncButton);

    await waitFor(() => {
      expect(screen.getByText('Operation completed successfully!')).toBeInTheDocument();
    });
  });

  test('info modal opens and closes', async () => {
    const user = userEvent.setup();
    render(<App />);
    
    await waitFor(() => {
      expect(screen.getByText('Show App Info')).toBeInTheDocument();
    });

    const infoButton = screen.getByText('Show App Info');
    await user.click(infoButton);

    await waitFor(() => {
      expect(screen.getByText(/Application Information:/)).toBeInTheDocument();
    });

    const closeButton = screen.getByText('Close');
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText(/Application Information:/)).not.toBeInTheDocument();
    });
  });
});
