import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Initial state
const initialState = {
  appInfo: {
    version: 'Loading...',
    platform: 'Loading...',
    electronVersion: 'Loading...',
    nodeVersion: 'Loading...'
  },
  status: {
    message: '',
    type: 'info',
    visible: false
  },
  isLoading: false
};

// Action types
export const ActionTypes = {
  SET_APP_INFO: 'SET_APP_INFO',
  SET_STATUS: 'SET_STATUS',
  HIDE_STATUS: 'HIDE_STATUS',
  SET_LOADING: 'SET_LOADING'
};

// Reducer
function appReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_APP_INFO:
      return {
        ...state,
        appInfo: { ...state.appInfo, ...action.payload }
      };
    case ActionTypes.SET_STATUS:
      return {
        ...state,
        status: {
          message: action.payload.message,
          type: action.payload.type || 'info',
          visible: true
        }
      };
    case ActionTypes.HIDE_STATUS:
      return {
        ...state,
        status: { ...state.status, visible: false }
      };
    case ActionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
    default:
      return state;
  }
}

// Context
const AppContext = createContext();

// Provider component
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load app information on mount
  useEffect(() => {
    loadAppInfo();
  }, []);

  const loadAppInfo = async () => {
    try {
      if (typeof window.electronAPI === 'undefined') {
        throw new Error('electronAPI not available');
      }

      // Get app version from main process
      const appVersion = await window.electronAPI.getAppVersion();
      
      // Get platform information
      const platform = await window.electronAPI.getPlatform();
      
      // Get Electron and Node.js versions
      const electronVersion = process.versions?.electron || 'N/A';
      const nodeVersion = process.versions?.node || 'N/A';

      dispatch({
        type: ActionTypes.SET_APP_INFO,
        payload: {
          version: appVersion,
          platform: getPlatformName(platform),
          electronVersion,
          nodeVersion
        }
      });

      showStatus('Application loaded successfully!', 'success');
    } catch (error) {
      console.error('Error loading app info:', error);
      showStatus('Error loading app information', 'error');
    }
  };

  const showStatus = (message, type = 'info') => {
    dispatch({
      type: ActionTypes.SET_STATUS,
      payload: { message, type }
    });

    // Auto-hide after 5 seconds for success/info messages
    if (type !== 'error') {
      setTimeout(() => {
        dispatch({ type: ActionTypes.HIDE_STATUS });
      }, 5000);
    }
  };

  const hideStatus = () => {
    dispatch({ type: ActionTypes.HIDE_STATUS });
  };

  const setLoading = (loading) => {
    dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
  };

  const performAsyncOperation = async () => {
    try {
      setLoading(true);
      showStatus('Performing async operation...', 'info');

      // Call the async operation in main process
      const result = await window.electronAPI.performAsyncOperation();
      
      showStatus(result, 'success');
      console.log('Async operation result:', result);
      return result;
    } catch (error) {
      console.error('Async operation failed:', error);
      showStatus('Async operation failed: ' + error.message, 'error');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    state,
    actions: {
      showStatus,
      hideStatus,
      setLoading,
      performAsyncOperation,
      loadAppInfo
    }
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

// Utility function
function getPlatformName(platform) {
  const platformNames = {
    'win32': 'Windows',
    'darwin': 'macOS',
    'linux': 'Linux',
    'freebsd': 'FreeBSD',
    'openbsd': 'OpenBSD',
    'sunos': 'SunOS'
  };
  return platformNames[platform] || platform;
}
