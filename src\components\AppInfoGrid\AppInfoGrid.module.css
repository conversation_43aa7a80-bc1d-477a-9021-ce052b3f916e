.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.infoItem {
  background: rgba(255, 255, 255, 0.7);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.label {
  font-size: 0.9em;
  font-weight: 600;
  color: #555;
  margin: 0 0 5px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.value {
  font-size: 1.1em;
  font-weight: 500;
  color: #333;
  margin: 0;
}

@media (max-width: 768px) {
  .infoGrid {
    grid-template-columns: 1fr;
  }
}
