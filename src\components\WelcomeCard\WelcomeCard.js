import { useState } from 'react';
import { Card, CardContent, Typography } from '@mui/material';
import { useApp } from '../../context/AppContext';
import AppInfoGrid from '../AppInfoGrid/AppInfoGrid';
import ActionButtons from '../ActionButtons/ActionButtons';
import InfoModal from '../InfoModal/InfoModal';

function WelcomeCard() {
  const [showInfoModal, setShowInfoModal] = useState(false);
  
  const handleShowInfo = () => {
    setShowInfoModal(true);
  };

  const handleCloseInfo = () => {
    setShowInfoModal(false);
  };

  return (
    <>
      <Card sx={{
        maxWidth: 600,
        width: '100%',
        mb: 3.75,
        p: 2
      }}>
        <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
          <Typography variant="h2" component="h2" sx={{ mb: 2.5 }}>
            Welcome to Your Electron React App!
          </Typography>
          <Typography variant="body1" sx={{ mb: 3.75 }}>
            This is a modern Electron application built with React, security best practices,
            and a clean architecture. The app demonstrates proper IPC communication,
            context isolation, and modern UI design patterns.
          </Typography>

          <AppInfoGrid />
          <ActionButtons onShowInfo={handleShowInfo} />
        </CardContent>
      </Card>

      {showInfoModal && <InfoModal onClose={handleCloseInfo} />}
    </>
  );
}

export default WelcomeCard;
