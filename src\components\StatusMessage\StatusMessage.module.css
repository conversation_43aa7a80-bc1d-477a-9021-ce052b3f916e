.status {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  border: 2px solid;
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 90%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status.show {
  opacity: 1;
}

.status.success {
  background: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.status.error {
  background: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.status.info {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1565c0;
}

.message {
  flex: 1;
}

.closeBtn {
  background: none;
  border: none;
  color: inherit;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.closeBtn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
