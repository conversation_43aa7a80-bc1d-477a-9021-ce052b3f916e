import { createTheme } from '@mui/material/styles';

// Custom theme that matches the current gradient design
const muiTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#667eea',
      light: '#9aa7ff',
      dark: '#3f51b5',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#764ba2',
      light: '#a478d4',
      dark: '#4a2c73',
      contrastText: '#ffffff',
    },
    tertiary: {
      main: '#f093fb',
      light: '#ffb3ff',
      dark: '#c162c8',
      contrastText: '#ffffff',
    },
    quaternary: {
      main: '#f5576c',
      light: '#ff8a9b',
      dark: '#c02440',
      contrastText: '#ffffff',
    },
    background: {
      default: 'transparent',
      paper: 'rgba(255, 255, 255, 0.95)',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
    success: {
      main: '#4caf50',
      light: '#81c784',
      dark: '#388e3c',
    },
    error: {
      main: '#f44336',
      light: '#e57373',
      dark: '#d32f2f',
    },
    info: {
      main: '#2196f3',
      light: '#64b5f6',
      dark: '#1976d2',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      'Helvetica',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 300,
      color: '#ffffff',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 400,
      color: '#333333',
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 500,
      color: '#333333',
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 500,
      color: '#333333',
    },
    h5: {
      fontSize: '1.1rem',
      fontWeight: 500,
      color: '#333333',
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      color: '#555555',
    },
    body1: {
      fontSize: '1.1rem',
      lineHeight: 1.6,
      color: '#666666',
    },
    body2: {
      fontSize: '1rem',
      lineHeight: 1.5,
      color: '#666666',
    },
    subtitle1: {
      fontSize: '1.1rem',
      color: 'rgba(255, 255, 255, 0.8)',
    },
    subtitle2: {
      fontSize: '0.9rem',
      fontWeight: 600,
      color: '#555555',
      textTransform: 'uppercase',
      letterSpacing: '0.5px',
    },
  },
  shape: {
    borderRadius: 15,
  },
  shadows: [
    'none',
    '0 4px 15px rgba(102, 126, 234, 0.3)',
    '0 6px 20px rgba(102, 126, 234, 0.4)',
    '0 8px 25px rgba(102, 126, 234, 0.5)',
    '0 10px 30px rgba(0, 0, 0, 0.1)',
    '0 20px 40px rgba(0, 0, 0, 0.1)',
    '0 20px 40px rgba(0, 0, 0, 0.3)',
    ...Array(18).fill('0 20px 40px rgba(0, 0, 0, 0.3)'),
  ],
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 25,
          textTransform: 'none',
          fontWeight: 500,
          minWidth: 160,
          padding: '12px 24px',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
          },
          '&:active': {
            transform: 'translateY(0)',
          },
          '&:disabled': {
            opacity: 0.6,
            transform: 'none',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)',
          },
        },
        containedSecondary: {
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          boxShadow: '0 4px 15px rgba(240, 147, 251, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            boxShadow: '0 6px 20px rgba(240, 147, 251, 0.4)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          background: 'rgba(255, 255, 255, 0.95)',
          borderRadius: 15,
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: 'none',
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 15,
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
        },
      },
    },
    MuiSnackbar: {
      styleOverrides: {
        root: {
          '& .MuiSnackbarContent-root': {
            borderRadius: 25,
            fontWeight: 500,
          },
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 25,
          fontWeight: 500,
          border: '2px solid',
        },
        standardSuccess: {
          backgroundColor: '#e8f5e8',
          borderColor: '#4caf50',
          color: '#2e7d32',
        },
        standardError: {
          backgroundColor: '#ffebee',
          borderColor: '#f44336',
          color: '#c62828',
        },
        standardInfo: {
          backgroundColor: '#e3f2fd',
          borderColor: '#2196f3',
          color: '#1565c0',
        },
      },
    },
  },
});

export default muiTheme;
