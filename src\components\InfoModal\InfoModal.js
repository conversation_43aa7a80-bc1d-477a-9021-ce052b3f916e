import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box
} from '@mui/material';
import { useApp } from '../../context/AppContext';

function InfoModal({ onClose }) {
  const { state } = useApp();
  const { appInfo } = state;

  const info = {
    'App Name': 'Electron React App',
    'Version': appInfo.version,
    'Platform': appInfo.platform,
    'Electron': appInfo.electronVersion,
    'Node.js': appInfo.nodeVersion,
    'Chrome': process.versions?.chrome || 'N/A',
    'V8': process.versions?.v8 || 'N/A'
  };

  let infoText = 'Application Information:\n\n';
  for (const [key, value] of Object.entries(info)) {
    infoText += `${key}: ${value}\n`;
  }

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { maxWidth: 500, width: '90%' }
      }}
    >
      <DialogTitle>
        Application Information
      </DialogTitle>
      <DialogContent>
        <Box component="pre" sx={{
          fontFamily: '"Courier New", monospace',
          fontSize: '14px',
          lineHeight: 1.5,
          color: 'text.primary',
          whiteSpace: 'pre-wrap',
          m: 0
        }}>
          {infoText}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default InfoModal;
