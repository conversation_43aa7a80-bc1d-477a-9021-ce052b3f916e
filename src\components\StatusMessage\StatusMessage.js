import { Snackbar, Alert, IconButton } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useApp } from '../../context/AppContext';

function StatusMessage() {
  const { state, actions } = useApp();
  const { status } = state;

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    actions.hideStatus();
  };

  const getSeverity = (type) => {
    switch (type) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'info': return 'info';
      default: return 'info';
    }
  };

  return (
    <Snackbar
      open={status.visible}
      autoHideDuration={status.type === 'error' ? null : 6000}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      sx={{
        '& .MuiSnackbarContent-root': {
          maxWidth: '90%',
        }
      }}
    >
      <Alert
        severity={getSeverity(status.type)}
        onClose={status.type === 'error' ? handleClose : undefined}
        sx={{ width: '100%' }}
      >
        {status.message}
      </Alert>
    </Snackbar>
  );
}

export default StatusMessage;
