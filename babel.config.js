module.exports = function(api) {
  api.cache(true);

  return {
    presets: [
      [
        '@babel/preset-env',
        {
          targets: {
            electron: '38'
          },
          modules: false
        }
      ],
      [
        '@babel/preset-react',
        {
          runtime: 'automatic'
        }
      ]
    ],
    env: {
      test: {
        presets: [
          [
            '@babel/preset-env',
            {
              targets: {
                node: 'current'
              }
            }
          ],
          [
            '@babel/preset-react',
            {
              runtime: 'automatic'
            }
          ]
        ]
      }
    }
  };
};
